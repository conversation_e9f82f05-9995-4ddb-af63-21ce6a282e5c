import React, { useEffect, useRef, useState } from "react";

import "./ChatDrawer.scss";
import { CloseOutlined } from "@ant-design/icons";
import PrivateChats from "./PrivateChats";
import PublicChats from "./PublicChats";
//   import {ReactComponent as Emojis} from "./icons/Emojis.svg";

/**
 * The Chat component adds a basis chat functionality to the LiveKit room. The messages are distributed to all participants
 * in the room. Only users who are in the room at the time of dispatch will receive the message.
 *
 * @example
 * ```jsx
 * <LiveKitRoom>
 *   <Chat />
 * </LiveKitRoom>
 * ```
 */
export default function ChatsDrawer({
  messageFormatter,
  messageDecoder,
  messageEncoder,
  channelTopic,
  privatechatparticipants,
  setprivatechatparticipants,
  selectedprivatechatparticipant,
  setselectedprivatechatparticipant,
  showPrivateChat,
  setShowPrivateChat,
  localparticipant,
  privatechatmessages,
  setprivatechatmessages,
  newmessagerender,
  setPrivateChatUnreadMessagesCount,
  privateChatUnreadMessagesCount,
  setPublicChatUnreadMessagesCount,
  publicChatUnreadMessagesCount,
  canDownloadChatAttachment,
  publicchatmessages,
  setpublicchatmessages,
  isWhiteboardOpen,
  // setToastNotification,
  // setToastStatus,
  // setShowToast,
  participantColors = new Map(),
  // Pinned messages
  privateChatPinnedMessages,
  setPrivateChatPinnedMessages,
  publicChatPinnedMessages,
  setPublicChatPinnedMessages,
  ...props
}) {
  const [highlightedMessage, setHighlightedMessage] = useState(null);
  const [showPrivateChatDrawer, setShowPrivateChatDrawer] =useState(showPrivateChat);
  const messageRefs = useRef({});

  useEffect(() => {
    if (showPrivateChat) {
      setShowPrivateChatDrawer(true);
    }
  }, [showPrivateChat]);

  const scrollToRepliedChat = (repliedID) => {
    const repliedElement = messageRefs.current[repliedID];

    if (repliedElement) {
      repliedElement.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }

    setHighlightedMessage(repliedID);
      setTimeout(() => {
        setHighlightedMessage(null);
    }, 2000);
  };
  
  return (
    <div 
      className={
        `sd-container 
        ${props.show ? "show" : "hide"} 
        chat-drawer
        ${isWhiteboardOpen ? "sd-container-whiteboard-open" : ""}`
      }>
      <div className="sd-container-inner">
        <div className="sd-header">
          <div className="sd-title primary-font">
            <span
              onClick={() => {
                setShowPrivateChatDrawer(false);
              }}
              className={showPrivateChatDrawer ? "" : "active"}
            >
              All Chats
              {publicChatUnreadMessagesCount > 0 &&(
                <div className="public-chat-count">{publicChatUnreadMessagesCount}</div>
              )}
            </span>
            <span
              onClick={() => {
                setShowPrivateChatDrawer(true);
              }}
              className={showPrivateChatDrawer ? "active" : ""}
            >
              Private Message
              {privateChatUnreadMessagesCount > 0 &&(
                <div className="private-chat-count">{privateChatUnreadMessagesCount}</div>
              )}
            </span>
          </div>
          <CloseOutlined onClick={()=>{
            props.setShow(false)
          }} className="chat-close-ico" />
        </div>
        <div className="sd-container-below">
          {!showPrivateChatDrawer ? (
            <PublicChats
              {...props}
              messageFormatter={messageFormatter}
              messageDecoder={messageDecoder}
              messageEncoder={messageEncoder}
              channelTopic={channelTopic}
              showprivatechatdrawer={showPrivateChatDrawer}
              setPublicChatUnreadMessagesCount={setPublicChatUnreadMessagesCount}
              publicChatUnreadMessagesCount={publicChatUnreadMessagesCount}
              showchatdrawer= {props.show}
              canDownloadChatAttachment={canDownloadChatAttachment}
              chatMessages={publicchatmessages}
              setChatMessages={setpublicchatmessages}
              localparticipant={localparticipant}
              // setToastNotification={setToastNotification}
              // setToastStatus={setToastStatus}
              // setShowToast={setShowToast}
              participantColors={participantColors}
              scrollToRepliedChat={scrollToRepliedChat}
              highlightedMessage={highlightedMessage}
              messageRefs={messageRefs}
              // Pinned messages
              publicChatPinnedMessages={publicChatPinnedMessages}
              setPublicChatPinnedMessages={setPublicChatPinnedMessages}
            />
          ) : (
            <PrivateChats
              messageFormatter={messageFormatter}
              privatechatparticipants={privatechatparticipants}
              setprivatechatparticipants={setprivatechatparticipants}
              selectedprivatechatparticipant={selectedprivatechatparticipant}
              setselectedprivatechatparticipant={
                setselectedprivatechatparticipant
              }
              localparticipant={localparticipant}
              privatechatmessages={privatechatmessages}
              setprivatechatmessages={setprivatechatmessages}
              newmessagerender={newmessagerender}
              showprivatechatdrawer={showPrivateChatDrawer}
              setPrivateChatUnreadMessagesCount={setPrivateChatUnreadMessagesCount}
              privateChatUnreadMessagesCount={privateChatUnreadMessagesCount}
              showchatdrawer= {props.show}
              canDownloadChatAttachment={canDownloadChatAttachment}
              // setToastNotification={setToastNotification}
              // setToastStatus={setToastStatus}
              // setShowToast={setShowToast}
              participantColors={participantColors}

              scrollToRepliedChat={scrollToRepliedChat}
              highlightedMessage={highlightedMessage}
              messageRefs={messageRefs}
              // Pinned messages
              privateChatPinnedMessages={privateChatPinnedMessages}
              setPrivateChatPinnedMessages={setPrivateChatPinnedMessages}
            />
          )}
        </div>
      </div>
    </div>
  );
}
