import { create } from 'zustand'
import { devtools } from 'zustand/middleware'


export const useIndexStore = create(
  devtools(
    (set, get) => ({
      // Example state (from original indexContext)
      exampleState: null,
      
      // Brightness state (from memories - main state in index.page.js)
      brightness: 100,
      participantBrightness: new Map(),
      
      // Noise suppression state
      noiseSuppressionEnabled: false,
      
      // Actions
      setExampleState: (state) => set({ exampleState: state }),
      
      // Brightness actions
      setBrightness: (value) => set({ brightness: value }),
      
      setParticipantBrightness: (participantId, brightness) => set((state) => {
        const newMap = new Map(state.participantBrightness)
        if (brightness === 100) {
          // Remove default brightness to keep map clean
          newMap.delete(participantId)
        } else {
          newMap.set(participantId, brightness)
        }
        return { participantBrightness: newMap }
      }),
      
      clearParticipantBrightness: (participantId) => set((state) => {
        const newMap = new Map(state.participantBrightness)
        newMap.delete(participantId)
        return { participantBrightness: newMap }
      }),
      
      clearAllParticipantBrightness: () => set({ participantBrightness: new Map() }),
      
      // Noise suppression actions
      setNoiseSuppressionEnabled: (enabled) => set({ noiseSuppressionEnabled: enabled }),
      
      // Reset all state (useful for cleanup)
      resetIndexState: () => set({
        exampleState: null,
        brightness: 100,
        participantBrightness: new Map(),
        noiseSuppressionEnabled: false,
      }),
    }),
    {
      name: 'index-store', // DevTools name
    }
  )
)

// Selectors for performance optimization
export const useBrightness = () => useIndexStore((state) => state.brightness)
export const useParticipantBrightness = () => useIndexStore((state) => state.participantBrightness)
export const useNoiseSuppressionEnabled = () => useIndexStore((state) => state.noiseSuppressionEnabled)

export const useBrightnessActions = () => useIndexStore((state) => ({
  setBrightness: state.setBrightness,
  setParticipantBrightness: state.setParticipantBrightness,
  clearParticipantBrightness: state.clearParticipantBrightness,
  clearAllParticipantBrightness: state.clearAllParticipantBrightness,
}))

export const useNoiseSuppressionActions = () => useIndexStore((state) => ({
  setNoiseSuppressionEnabled: state.setNoiseSuppressionEnabled,
}))
