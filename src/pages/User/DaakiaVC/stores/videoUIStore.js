import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Video UI Store - replaces VideoConferencesContext.js
export const useVideoUIStore = create(
  devtools(
    (set, get) => ({
      // UI State - Drawer Management
      showParticipantsList: false,
      
      // Actions
      setShowParticipantsList: (show) => set({ showParticipantsList: show }),
      toggleParticipantsList: () => set((state) => ({ 
        showParticipantsList: !state.showParticipantsList 
      })),
      
      // Reset all UI state
      resetUIState: () => set({
        showParticipantsList: false,
      }),
    }),
    {
      name: 'video-ui-store', // DevTools name
    }
  )
)

// Selectors for performance optimization
export const useShowParticipantsList = () => useVideoUIStore((state) => state.showParticipantsList)
export const useParticipantsListActions = () => useVideoUIStore((state) => ({
  setShowParticipantsList: state.setShowParticipantsList,
  toggleParticipantsList: state.toggleParticipantsList,
}))
