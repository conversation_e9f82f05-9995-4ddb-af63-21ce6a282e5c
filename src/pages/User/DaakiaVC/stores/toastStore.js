import { create } from 'zustand'
import { devtools } from 'zustand/middleware'


export const useToastStore = create(
  devtools(
    (set, get) => ({
      toastNotification: null,
      
      // Actions
      setToastNotification: (notification) => set({ toastNotification: notification }),
      
      clearToastNotification: () => set({ toastNotification: null }),
      
      // Helper to show different types of toasts
      showSuccessToast: (message) => set({ 
        toastNotification: { type: 'success', message } 
      }),
      
      showErrorToast: (message) => set({ 
        toastNotification: { type: 'error', message } 
      }),
      
      showInfoToast: (message) => set({ 
        toastNotification: { type: 'info', message } 
      }),
      
      showWarningToast: (message) => set({ 
        toastNotification: { type: 'warning', message } 
      }),
    }),
    {
      name: 'toast-store', // DevTools name
    }
  )
)

export const useToastNotification = () => useToastStore((state) => state.toastNotification)

export const useToastActions = () => useToastStore((state) => ({
  setToastNotification: state.setToastNotification,
  clearToastNotification: state.clearToastNotification,
  showSuccessToast: state.showSuccessToast,
  showErrorToast: state.showErrorToast,
  showInfoToast: state.showInfoToast,
  showWarningToast: state.showWarningToast,
}))
